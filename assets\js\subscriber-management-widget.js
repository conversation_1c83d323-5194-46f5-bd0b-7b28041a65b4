(function($) {
    'use strict';

    $(document).ready(function() {
        // Inizializzazione del widget
        initSubscriberManagementWidget();
    });

    function initSubscriberManagementWidget() {
        // Menu navigation
        $('.menu-item').on('click', function() {
            const section = $(this).data('section');
            switchSection(section);
        });

        // Form submission per aggiornamento dati
        $('#access-data-form').on('submit', function(e) {
            e.preventDefault();
            updateSubscriberData();
        });

        // Amount buttons per ricarica crediti
        $('.amount-btn').on('click', function() {
            const amount = $(this).data('amount');
            selectAmount(amount);
        });

        // Custom amount input
        $('#custom-amount-input').on('input', function() {
            const amount = parseFloat($(this).val());
            if (amount >= 5) {
                selectAmount(amount);
                $('.amount-btn').removeClass('selected');
            }
        });

        // Payment method selection
        $('.payment-method').on('click', function() {
            const method = $(this).data('method');
            selectPaymentMethod(method);
        });

        // Proceed with recharge
        $('#proceed-recharge-btn').on('click', function() {
            proceedWithRecharge();
        });
    }

    function switchSection(section) {
        // Update menu
        $('.menu-item').removeClass('active');
        $('.menu-item[data-section="' + section + '"]').addClass('active');

        // Update content
        $('.content-section').removeClass('active');
        $('#' + section + '-section').addClass('active');
    }

    function updateSubscriberData() {
        const formData = {
            action: 'update_subscriber_data',
            nonce: subscriberManagementAjax.nonce,
            subscriber_id: $('input[name="subscriber_id"]').val(),
            name: $('#subscriber-name').val(),
            surname: $('#subscriber-surname').val(),
            phone: $('#subscriber-phone').val()
        };

        // Show loading state
        const $submitBtn = $('#access-data-form button[type="submit"]');
        const originalText = $submitBtn.html();
        $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Aggiornamento...').prop('disabled', true);

        $.ajax({
            url: subscriberManagementAjax.ajax_url,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(subscriberManagementAjax.messages.update_error, 'error');
            },
            complete: function() {
                // Restore button state
                $submitBtn.html(originalText).prop('disabled', false);
            }
        });
    }

    function selectAmount(amount) {
        $('#selected-amount').val(amount);
        $('.amount-btn').removeClass('selected');
        $('.amount-btn[data-amount="' + amount + '"]').addClass('selected');
        
        // Clear custom input if predefined amount is selected
        if ($('.amount-btn[data-amount="' + amount + '"]').length > 0) {
            $('#custom-amount-input').val('');
        }
        
        updateRechargeButton();
    }

    function selectPaymentMethod(method) {
        $('#selected-method').val(method);
        $('.payment-method').removeClass('selected');
        $('.payment-method[data-method="' + method + '"]').addClass('selected');
        updateRechargeButton();
    }

    function updateRechargeButton() {
        const amount = $('#selected-amount').val();
        const method = $('#selected-method').val();
        const $btn = $('#proceed-recharge-btn');
        
        if (amount && method && parseFloat(amount) >= 5) {
            $btn.prop('disabled', false);
            $btn.html('<i class="fas fa-shopping-cart"></i> Ricarica €' + parseFloat(amount).toFixed(2));
        } else {
            $btn.prop('disabled', true);
            $btn.html('<i class="fas fa-shopping-cart"></i> Procedi con la Ricarica');
        }
    }

    function proceedWithRecharge() {
        const amount = parseFloat($('#selected-amount').val());
        const method = $('#selected-method').val();
        const subscriberId = $('#subscriber-id').val();

        if (!amount || !method || !subscriberId) {
            showFeedbackMessage('Seleziona un importo e un metodo di pagamento', 'error');
            return;
        }

        if (amount < 5) {
            showFeedbackMessage('L\'importo minimo è €5.00', 'error');
            return;
        }

        // Conferma dell'utente
        if (!confirm('Confermi la ricarica di €' + amount.toFixed(2) + ' tramite ' + getPaymentMethodName(method) + '?')) {
            return;
        }

        const $btn = $('#proceed-recharge-btn');
        const originalText = $btn.html();
        $btn.html('<i class="fas fa-spinner fa-spin"></i> Elaborazione...').prop('disabled', true);

        const rechargeData = {
            action: 'recharge_credits',
            nonce: subscriberManagementAjax.nonce,
            subscriber_id: subscriberId,
            amount: amount,
            method: method
        };

        $.ajax({
            url: subscriberManagementAjax.ajax_url,
            type: 'POST',
            data: rechargeData,
            success: function(response) {
                if (response.success) {
                    showFeedbackMessage(response.data.message, 'success');
                    
                    // Update credit display
                    if (response.data.new_credit) {
                        $('.credit-value').text('€' + response.data.new_credit);
                        $('.stats-card.credit-card .stats-value').text('€' + response.data.new_credit);
                    }
                    
                    // Reset form
                    resetRechargeForm();
                } else {
                    showFeedbackMessage(response.data.message, 'error');
                }
            },
            error: function() {
                showFeedbackMessage(subscriberManagementAjax.messages.recharge_error, 'error');
            },
            complete: function() {
                $btn.html(originalText).prop('disabled', true);
            }
        });
    }

    function resetRechargeForm() {
        $('#selected-amount').val('');
        $('#selected-method').val('');
        $('#custom-amount-input').val('');
        $('.amount-btn').removeClass('selected');
        $('.payment-method').removeClass('selected');
        updateRechargeButton();
    }

    function getPaymentMethodName(method) {
        const methods = {
            'paypal': 'PayPal',
            'stripe': 'Carta di Credito',
            'bank': 'Bonifico Bancario'
        };
        return methods[method] || method;
    }

    function showFeedbackMessage(message, type) {
        const $feedback = $('#subscriber-feedback-message');
        
        $feedback
            .removeClass('success error')
            .addClass(type)
            .text(message)
            .fadeIn();

        // Auto hide after 5 seconds
        setTimeout(function() {
            $feedback.fadeOut();
        }, 5000);
    }

    // Utility function to format currency
    function formatCurrency(amount) {
        return '€' + parseFloat(amount).toFixed(2).replace('.', ',');
    }

    // Export functions for external use
    window.subscriberManagementWidget = {
        switchSection: switchSection,
        showFeedbackMessage: showFeedbackMessage,
        formatCurrency: formatCurrency
    };

})(jQuery);
