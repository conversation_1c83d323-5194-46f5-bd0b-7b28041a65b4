.document-viewer-widget {
    max-width: 100%;
    margin: 20px 0;
    padding: 30px;
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    position: relative;
}

/* Reset the title styling to allow for new positioning */
.document-viewer-widget h3 {
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    letter-spacing: 0.02em;
}

/* New styling for the dossier column (left) - 10% */
.dossier-column {
    flex: 0 0 10%;
    min-width: 100px;
    max-width: 10%;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #eee;
}

.dossier-column h3 {
    margin-top: 0;
    text-align: left;
    color: #333;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.dossier-content {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #555;
}

/* Adjust the form column (center) - 30% */
.document-form-column {
    flex: 0 0 30%;
    min-width: 250px;
    max-width: 30%;
    position: relative;
    background-color: #fafafa;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #f0f0f0;
}

.document-form-column h3 {
    margin-top: 0;
    text-align: center;
    margin-bottom: 25px;
    position: relative;
}

.document-form-column h3:after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: #0073aa;
    margin: 8px auto 0;
    border-radius: 2px;
}

/* Adjust the display column (right) - 60% */
.document-display-column {
    flex: 0 0 60%;
    min-width: 300px; /* Larghezza minima per garantire usabilità */
    max-width: 60%;
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #eee;
}

/* Responsive design: stack columns on small screens */
@media (max-width: 992px) {
    .document-viewer-widget {
        flex-direction: column;
    }

    .dossier-column,
    .document-form-column,
    .document-display-column {
        width: 100%;
        max-width: 100%;
    }
}

/* Title styling to ensure it spans full width and stays centered */
.document-viewer-widget h3 {
    width: 100%;
    text-align: center;
    margin-bottom: 20px;
    flex-basis: 100%;
}

/* Questi stili sono stati rimossi perché causano un override delle proprietà flex */
/* La configurazione corretta delle colonne è definita sopra */

/* Responsive design: su schermi piccoli, impila le colonne */
@media (max-width: 768px) {
    .document-viewer-widget {
        flex-direction: column;
    }

    .document-form-column,
    .document-display-column {
        width: 100%;
    }
}

/* System per i risultati dell'analisi compatti */
.compact-analysis {
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 20px;
}

.analysis-container {
    position: relative;
    margin: 15px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
    overflow: hidden;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ddd;
}

.analysis-title {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.analysis-controls {
    display: flex;
    gap: 10px;
}

.toggle-view-btn {
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.toggle-view-btn:hover {
    background-color: #f5f5f5;
    transform: scale(1.05);
}

.toggle-view-btn .compress-icon,
.toggle-view-btn .expand-icon {
    font-size: 18px;
    font-weight: bold;
    color: #555;
}

.analysis-content {
    padding: 15px;
    position: relative;
    overflow: hidden;
    max-height: 500px; /* Altezza massima complessiva per il contenitore */
}

.analysis-tabs {
    display: flex;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.analysis-tab {
    padding: 10px 15px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    border-right: 1px solid #e0e0e0;
}

.analysis-tab.active {
    background-color: #fff;
    border-bottom: 2px solid #0073aa;
}

.analysis-tab:hover:not(.active) {
    background-color: #f0f0f0;
}

.tab-content {
    display: none;
    padding: 15px;
    background-color: #fff;
    max-height: 400px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
    max-height: 400px;
    overflow-y: auto;
}

#analysis-content {
    min-height: 100px;
    overflow-y: auto !important; /* Forza la visualizzazione della scrollbar quando necessario */
    line-height: 1.5;
    max-height: 370px;
    padding-right: 10px; /* Spazio per la scrollbar */
}

.analysis-toggle-btn {
    display: block;
    width: 100%;
    padding: 8px;
    background-color: #f5f5f5;
    border: none;
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    border-top: 1px solid #e0e0e0;
}

.analysis-toggle-btn:hover {
    background-color: #f0f0f0;
}

.analysis-summary {
    padding: 10px 15px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #e0e0e0;
    font-style: italic;
    color: #555;
}

/* Custom logo upload section styling */
.form-row {
    margin-bottom: 15px;
}

/* Logo upload styling */
.custom-logo-field {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.custom-logo-field input[type="file"] {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
}

.custom-logo-field label[for="custom-logo-upload"] {
    display: inline-block;
    padding: 8px 14px;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background-color: #1a4b78;
    color: white;
    border-radius: 4px;
}

.custom-logo-field label[for="custom-logo-upload"]:hover {
    background-color: #0d3a67;
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

.custom-logo-field label[for="custom-logo-upload"]:first-of-type {
    order: 1;
}

.custom-logo-field label.logo-description {
    background: none;
    box-shadow: none;
    color: #333;
    padding: 0;
    font-weight: normal;
    order: 2;
}

.custom-logo-field label.logo-description:hover {
    background: none;
    box-shadow: none;
    transform: none;
}

.logo-preview-container {
    width: 100%;
    margin-top: 10px;
    order: 3;
}

#logo-preview {
    max-width: 200px;
    max-height: 60px;
    border: 1px solid #ddd;
    padding: 5px;
    background: #fff;
}

#logo-dimensions-info {
    font-size: 11px;
    color: #666;
    margin-top: 3px;
}

/* PDF export button styling */
#export-pdf {
    background-color: #d9534f;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    margin-top: 10px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

#export-pdf:hover {
    background-color: #c9302c;
}

#export-pdf:before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="white"><path d="M14,0H2C0.9,0,0,0.9,0,2v12c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V2C16,0.9,15.1,0,14,0z M5,14H3v-2h2V14z M5,10H3V8h2V10z M5,6H3V4h2V6z M9,14H7v-2h2V14z M9,10H7V8h2V10z M9,6H7V4h2V6z M13,14h-2v-2h2V14z M13,10h-2V8h2V10z M13,6h-2V4h2V6z"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

/* PDF export section */
.export-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* Style for success messages */
.success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
    padding: 10px;
    border-radius: 4px;
}

/* Style for error messages */
.error {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
    padding: 10px;
    border-radius: 4px;
}

/* Basic responsive container */
.document-viewer-widget {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px 0;
}

/* Chat widget styling */
.chat-widget {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
    background-color: #f9f9f9;
}

.chat-messages {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
    background-color: white;
    margin-bottom: 15px;
}

.chat-input {
    display: flex;
    gap: 10px;
}

.chat-input textarea {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    min-height: 60px;
}

.chat-input button {
    align-self: flex-end;
    padding: 8px 15px;
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.user-message {
    background-color: #e3f2fd;
    padding: 8px 12px;
    border-radius: 18px 18px 0 18px;
    margin: 8px 0;
    align-self: flex-end;
    max-width: 80%;
    margin-left: auto;
}

.bot-message {
    background-color: #f1f1f1;
    padding: 8px 12px;
    border-radius: 18px 18px 18px 0;
    margin: 8px 0;
    align-self: flex-start;
    max-width: 80%;
}

#chat-typing, #settings-chat-typing {
    font-style: italic;
    color: #666;
    margin: 5px 0;
    display: none;
}

.dots {
    display: inline-block;
    width: 20px;
    text-align: left;
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 33% { content: '.'; }
    34%, 66% { content: '..'; }
    67%, 100% { content: '...'; }
}

/* Document frame container with flex layout */
#document-display {
    border: 1px solid #ddd;
    margin-bottom: 15px;
    cursor: grab;
    overflow: hidden !important; /* Impostiamo hidden con !important per avere priorità massima */
    position: relative;
    height: 100% !important; /* Imposta l'altezza al 100% del contenitore */
    min-height: 400px; /* Altezza minima ridotta per adattarsi meglio al form */
    max-height: 800px; /* Limita l'altezza massima */
    width: 100%;
    display: flex; /* Garantisce che l'iframe si adatti correttamente */
    align-items: center; /* Centra verticalmente il contenuto */
    justify-content: center; /* Centra orizzontalmente il contenuto */
    background-color: #f5f5f5; /* Sfondo leggermente più visibile */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#document-display:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

#document-display.focus-view {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

#document-display.grabbing {
    cursor: grabbing;
}

/* Stile migliorato per l'iframe che contiene il documento */
#document-frame {
    border: none;
    width: 100%;
    height: 100%;
    transform-origin: top left;
    display: block; /* Garantisce che l'iframe si comporti come un blocco */
    background-color: white; /* Sfondo bianco per il documento */
    box-shadow: 0 2px 8px rgba(0,0,0,0.1); /* Ombra leggera per dare profondità */

}

/* Simulazione di un foglio A4 nel visualizzatore */
.a4-container {
    position: relative;
    width: calc(100% - 20px); /* Larghezza leggermente ridotta per mostrare i bordi */
    height: 100%;
    max-width: 595px; /* Larghezza standard A4 in pixel (72dpi) */
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Zoom controls migliorati */
.zoom-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    background: rgba(255,255,255,0.9);
    border-radius: 5px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.zoom-btn {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.2s ease;
}

.zoom-btn:hover {
    background: #e1e1e1;
    transform: scale(1.05);
}

/* Responsive adjustments for A4 paper size */
@media (max-width: 992px) {
    #document-display {
        max-height: 700px;
    }
}

@media (max-width: 768px) {
    #document-display {
        max-height: 600px;
    }
}

@media (max-width: 600px) {
    #document-display {
        max-height: 500px;
    }

    .zoom-controls {
        top: 5px;
        right: 5px;
    }

    .zoom-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }
}

/* Zoom controls */
.zoom-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.zoom-btn {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
}

.zoom-btn:hover {
    background: #e1e1e1;
}

/* Form styling ripristinato */
.document-form-column .form-row {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.document-form-column label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    margin-bottom: 5px;
    display: block;
}

.document-form-column input[type="text"],
.document-form-column textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    font-size: 14px;
    color: #333;
    background-color: #fff;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
    resize: vertical;
}

.document-form-column input[type="text"]:focus,
.document-form-column textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

.document-form-column textarea {
    min-height: 80px;
    line-height: 1.5;
    font-family: inherit;
}

/* Analysis form styling */
.analysis-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.analysis-form label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
    color: #333;
}

.analysis-form textarea,
.analysis-form input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.analysis-form textarea:focus,
.analysis-form input[type="text"]:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

.analysis-form button {
    padding: 12px 20px;
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    align-self: flex-start;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.analysis-form button:hover {
    background-color: #005177;
}

/* Document controller */
.document-controller {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.document-actions button {
    background-color: #f7f7f7;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
}

.document-actions button:hover {
    background-color: #f0f0f0;
}

/* Enhanced media query support */
@media (max-width: 768px) {
    .document-controller {
        flex-direction: column;
    }

    .analysis-form button {
        width: 100%;
    }

    .export-actions {
        flex-direction: column;
    }

    .chat-input {
        flex-direction: column;
    }

    .chat-input button {
        align-self: stretch;
    }
}

/* Add responsive styles for the document viewer */
@media (max-width: 600px) {
    #document-display {
        height: 500px !important; /* Force a reasonable height on small screens */
    }

    .zoom-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 100;
        background: rgba(255,255,255,0.8);
        border-radius: 5px;
        padding: 5px;
    }
}

/* Button styling ripristinato - layout originale */
.document-form-column button {
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-right: 10px;
    margin-bottom: 10px;
    display: inline-block;
}

/* Layout pulsanti in riga */
.document-form-column .form-row button {
    display: inline-block;
    margin-right: 10px;
}

/* Stile default per tutti i pulsanti - blu scuro */
#analyze-description, #export-pdf, button[type="button"]:not(#clear-document) {
    background-color: #1a4b78; /* Blu scuro */
    color: white;
}

#analyze-description:hover, #export-pdf:hover, button[type="button"]:not(#clear-document):hover {
    background-color: #0d3a67; /* Blu più scuro al passaggio del mouse */
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

/* Stile specifico per il pulsante Clear - rosso */
#clear-document {
    background-color: #d63638; /* Rosso */
    color: white;
}

#clear-document:hover {
    background-color: #b32d2e; /* Rosso più scuro al passaggio del mouse */
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

/* Stile per i pulsanti attivi/premuti */
.document-form-column button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Stile per i pulsanti disabilitati */
.document-form-column button:disabled {
    background-color: #cccccc;
    color: #666666;
    cursor: not-allowed;
    box-shadow: none;
}

/* Icone rimosse per stile originale */
#analyze-description:before,
#export-pdf:before,
#clear-document:before {
    content: none;
}

/* Export actions - layout allineato e equidimensionato */
.export-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e1e1e1;
    align-items: flex-start;
}

.export-actions button {
    background-color: #1a4b78 !important;
    color: white !important;
    flex: 1;
    min-width: 140px;
    max-width: 180px;
    padding: 12px 16px;
    text-align: center;
    white-space: nowrap;
}

.export-actions button:hover {
    background-color: #0d3a67 !important;
}

/* Pulsanti allineati correttamente */
.document-form-column button {
    display: inline-block;
    vertical-align: top;
}

/* Styling per i pulsanti di input file per uniformarli allo stile dei pulsanti blu */
input[type="file"] {
    width: 0.1px;
    height: 0.1px;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    z-index: -1;
}

input[type="file"] + label {
    display: inline-block;
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-right: 8px;
    margin-bottom: 8px;
    background-color: #1a4b78; /* Blu scuro come gli altri pulsanti */
    color: white;
}

input[type="file"] + label:hover {
    background-color: #0d3a67; /* Blu più scuro al passaggio del mouse */
    box-shadow: 0 3px 6px rgba(0,0,0,0.16);
    transform: translateY(-1px);
}

input[type="file"] + label:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Icona rimossa dal pulsante di upload */
input[type="file"] + label:before {
    content: none;
}

/* Stile per il messaggio di estrazione completata */
.extraction-success {
    background-color: #e8f5e9;
    border-left: 4px solid #4CAF50;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    animation: fadeInUp 0.5s ease;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.extraction-success p {
    margin: 5px 0;
}

.extraction-success strong {
    color: #2e7d32;
}

/* Animazione per il messaggio di estrazione */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stile per l'area informazioni documento */
#document-info {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    font-size: 13px;
    display: none; /* Nascosto inizialmente */
}

#document-info p {
    margin: 5px 0;
    line-height: 1.4;
}

/* Stile per il pannello di notifica */
#document-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    background-color: #4CAF50;
    color: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    max-width: 300px;
    opacity: 0;
    transform: translateY(-20px);
    animation: notificationFade 0.3s forwards;
}

#document-notification h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
}

#document-notification p {
    margin: 0;
    font-size: 14px;
}

@keyframes notificationFade {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ottimizzazione delle notifiche per scomparire gradualmente */
.document-notification-area {
    background-color: #f8f9f8;
    border-radius: 4px;
    margin-bottom: 15px;
    padding: 10px 15px;
    border-left: 4px solid #4CAF50;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    font-size: 14px;
    line-height: 1.5;
    animation: fadein 0.5s;
    transition: opacity 0.3s ease, transform 0.3s ease;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    opacity: 0.95;
}

.document-notification-area.error {
    border-left: 4px solid #f44336;
    background-color: #fff8f8;
}

.document-notification-area.processing {
    border-left: 4px solid #2196F3;
    background-color: #f6f9ff;
}

@keyframes fadein {
    from { opacity: 0; transform: translateY(-20px); }
    to   { opacity: 1; transform: translateY(0); }
}

@keyframes fadeout {
    from { opacity: 1; transform: translateY(0); }
    to   { opacity: 0; transform: translateY(-20px); }
}

.notification-content {
    position: relative;
}

/* Spinner for processing notifications */
.notification-content .spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid rgba(0,0,0,0.1);
    border-top-color: #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Area informazioni inline con transizione a scomparsa */
#document-info-inline {
    margin: 10px 0;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9em;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    transition: opacity 0.5s ease;
}

#document-info-inline.fadeout {
    opacity: 0;
    height: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
}

/* Save result message styling */
.save-result-message {
    margin-top: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    display: none;
}

.save-result-message.success {
    display: block;
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 4px solid #4CAF50;
    animation: fadeIn 0.5s ease;
}

.save-result-message.error {
    display: block;
    background-color: #ffebee;
    color: #c62828;
    border-left: 4px solid #f44336;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Stile specifico per il pulsante Salva Analisi - verde scuro */
#save-analysis {
    background-color: #2e7d32 !important; /* Verde scuro */
    color: white !important;
}

#save-analysis:hover {
    background-color: #1b5e20 !important; /* Verde più scuro al passaggio del mouse */
}

/* Icona rimossa dal pulsante Salva Analisi */
#save-analysis:before {
    content: none;
}

/* Stile per il menu a tendina delle richieste predefinite */
.preset-queries-container {
    position: relative;
    margin-bottom: 10px;
    width: 100%;
}

.preset-queries-select {
    width: 100%;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preset-queries-select:hover {
    background-color: #f0f0f0;
    border-color: #ccc;
}

.preset-queries-select:focus {
    outline: none;
    border-color: #1a4b78;
    box-shadow: 0 0 5px rgba(26, 75, 120, 0.3);
}

.preset-info-tip {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: #1a4b78;
    color: white;
    text-align: center;
    font-size: 12px;
    line-height: 18px;
    cursor: help;
    pointer-events: none;
}

/* Animazione per la visualizzazione delle richieste nel textarea */
@keyframes highlightTextarea {
    0% { background-color: #f8f9f8; }
    20% { background-color: #e3f2fd; }
    100% { background-color: #f8f9f8; }
}

.textarea-highlight {
    animation: highlightTextarea 1.5s ease;
}

/* Stile responsive per i dispositivi mobili */
@media (max-width: 768px) {
    .preset-queries-select {
        font-size: 13px;
        padding: 8px;
    }

    .preset-info-tip {
        width: 16px;
        height: 16px;
        line-height: 16px;
        font-size: 10px;
    }
}

/* Dynamic styles moved from shortcode inline styling */
.document-viewer-widget {
    max-width: 100%;
}

#document-frame {
    width: 100%;
}

/* File upload with inline info - layout ripristinato */
.file-upload-container {
    display: block;
    width: 100%;
}

.file-upload-input {
    display: block;
    margin-bottom: 10px;
}

.file-upload-input input[type="file"] + label {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 5px;
}

.document-info-inline {
    display: block;
    width: 100%;
    font-size: 0.9em;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #0073aa;
    margin-top: 10px;
}

.document-info-inline span {
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 5px;
    white-space: nowrap;
}

/* Layout corretto per le etichette e descrizioni */
.logo-description {
    display: block !important;
    font-size: 12px !important;
    color: #666 !important;
    margin-top: 5px !important;
    font-weight: normal !important;
}

/* Optional: Custom instance-specific branding */
.document-viewer-widget h3 {
    color: #2c3e50;
}

/* Enhanced styling for the instructions panel */
.instructions-panel {
    margin: 15px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 3px solid #0073aa;
    font-size: 0.9em;
}

.instructions-panel h4 {
    margin-top: 0;
    color: #0073aa;
}

.instructions-panel ol {
    padding-left: 25px;
    margin-bottom: 0;
}

.instructions-panel li {
    margin-bottom: 5px;
}

/* Spinner per indicazione caricamento */
.analysis-loading-indicator {
    text-align: center;
    padding: 30px;
    background-color: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #eee;
    margin: 20px 0;
}

.analysis-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 5px solid rgba(0, 115, 170, 0.2);
    border-radius: 50%;
    border-top-color: #0073aa;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 15px;
}

.analysis-wait-message {
    font-style: italic;
    color: #777;
    font-size: 0.9em;
    margin-top: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* L'indicatore di caricamento viene visualizzato quando l'analisi è in corso */
.analysis-loading-indicator p {
    margin: 5px 0;
    font-weight: 500;
    color: #333;
}

/* Effetto Blink per i campi che vengono aggiornati dopo caricamento documento */
.blink-effect {
    animation: blink-animation 4s ease-in-out;
    background-color: rgba(220, 255, 220, 0.9) !important; /* Sfondo verdino più soft */
    box-shadow: 0 0 8px rgba(120, 200, 120, 0.6);
    border-color: #8cd98c !important; /* Bordo verde chiaro */
    transition: background-color 2s ease-out, box-shadow 2s ease-out, border-color 2s ease-out;
}

@keyframes blink-animation {
    0% {
        background-color: rgba(220, 255, 220, 0.9);
        box-shadow: 0 0 8px rgba(120, 200, 120, 0.6);
    }
    25% {
        background-color: rgba(200, 255, 200, 0.95);
        box-shadow: 0 0 12px rgba(100, 200, 100, 0.8);
    }
    50% {
        background-color: rgba(180, 255, 180, 1);
        box-shadow: 0 0 15px rgba(80, 200, 80, 0.9);
    }
    75% {
        background-color: rgba(200, 255, 200, 0.95);
        box-shadow: 0 0 12px rgba(100, 200, 100, 0.8);
    }
    100% {
        background-color: rgba(220, 255, 220, 0.9);
        box-shadow: 0 0 8px rgba(120, 200, 120, 0.6);
    }
}

/* Miglioramento stile per il campo token */
#document-tokens-inline {
    padding: 3px 8px;
    border-radius: 4px;
    display: inline-block;
    transition: all 0.3s ease;
}

/* Miglioramento stile per il menu domande preset */
.preset-queries-select {
    transition: all 0.3s ease;
    border-radius: 4px;
}