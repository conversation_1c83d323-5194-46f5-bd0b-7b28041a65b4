/**
 * Subscriber Management Widget CSS
 * Stile allineato con gli altri widget del plugin
 */

/* Container principale */
.subscriber-management-widget-container {
    max-width: 100%;
    margin: 20px 0;
    padding: 0;
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    display: flex;
    min-height: 500px;
    position: relative;
    overflow: hidden;
}

/* Menu sinistro */
.subscriber-menu-column {
    flex: 0 0 250px;
    background: #f8f9fa;
    border-right: 1px solid #e1e1e1;
    padding: 0;
}

.subscriber-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    border-bottom: 1px solid #e1e1e1;
    transition: all 0.3s ease;
    color: #555;
    font-weight: 500;
}

.menu-item:hover {
    background-color: #e9ecef;
    color: #333;
}

.menu-item.active {
    background-color: #0073aa;
    color: white;
    border-left: 4px solid #005177;
}

.menu-item i {
    margin-right: 12px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.menu-item span {
    font-size: 14px;
}

/* Contenuto principale */
.subscriber-content-column {
    flex: 1;
    padding: 30px;
    background: #fff;
    position: relative;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Header delle sezioni */
.section-header {
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.section-header h3 {
    margin: 0 0 8px 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
}

.section-header p {
    margin: 0;
    color: #666;
    font-size: 0.95rem;
}

/* Form styling */
.subscriber-form {
    max-width: 600px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.95rem;
    color: #444;
    background-color: #fff;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
    outline: none;
}

.form-group input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.form-actions {
    margin-top: 30px;
}

.btn-primary {
    padding: 12px 24px;
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    background-color: #005177;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Stats grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stats-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stats-card.credit-card {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.stats-icon {
    flex: 0 0 50px;
    height: 50px;
    background: #0073aa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.credit-card .stats-icon {
    background: #28a745;
}

.stats-content {
    flex: 1;
}

.stats-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.stats-label {
    font-size: 0.85rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Consumption details */
.consumption-details {
    background: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
    padding: 20px;
}

.consumption-details h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: #333;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #555;
}

.detail-value {
    font-weight: 600;
    color: #333;
}

/* Credit recharge section */
.current-credit-display {
    background: linear-gradient(135deg, #e8f5e8 0%, #f8fff9 100%);
    border: 1px solid #28a745;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: center;
}

.credit-amount {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.credit-label {
    font-size: 1.1rem;
    color: #555;
}

.credit-value {
    font-size: 2rem;
    font-weight: 700;
    color: #28a745;
}

/* Recharge options */
.recharge-options {
    margin-bottom: 30px;
}

.recharge-options h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: #333;
}

.amount-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.amount-btn {
    padding: 10px 20px;
    border: 2px solid #0073aa;
    background: white;
    color: #0073aa;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.amount-btn:hover,
.amount-btn.selected {
    background: #0073aa;
    color: white;
}

.custom-amount {
    margin-top: 20px;
}

.custom-amount label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.amount-input-group {
    display: flex;
    align-items: center;
    max-width: 200px;
}

.currency-symbol {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-right: none;
    padding: 10px 12px;
    border-radius: 4px 0 0 4px;
    font-weight: 600;
    color: #555;
}

.amount-input-group input {
    border-radius: 0 4px 4px 0;
    border-left: none;
}

/* Payment methods */
.payment-methods {
    margin-bottom: 30px;
}

.payment-methods h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: #333;
}

.payment-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.payment-method {
    flex: 1;
    min-width: 150px;
    padding: 15px;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.payment-method:hover,
.payment-method.selected {
    border-color: #0073aa;
    background: #f0f8ff;
}

.payment-method i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
    color: #0073aa;
}

.payment-method span {
    font-weight: 600;
    color: #333;
}

/* Feedback messages */
.feedback-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    font-weight: 500;
    z-index: 9999;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.feedback-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.feedback-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Login required message */
.login-required-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.login-required-message h3 {
    margin-bottom: 15px;
    color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
    .subscriber-management-widget-container {
        flex-direction: column;
        min-height: auto;
    }
    
    .subscriber-menu-column {
        flex: none;
        border-right: none;
        border-bottom: 1px solid #e1e1e1;
    }
    
    .subscriber-menu {
        display: flex;
        overflow-x: auto;
    }
    
    .menu-item {
        flex: 0 0 auto;
        white-space: nowrap;
        border-bottom: none;
        border-right: 1px solid #e1e1e1;
    }
    
    .subscriber-content-column {
        padding: 20px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .amount-buttons {
        justify-content: center;
    }
    
    .payment-options {
        flex-direction: column;
    }
}
