# Subscriber Management Widget - Documentazione

## Panoramica delle Modifiche

Questo documento descrive le modifiche implementate per il sistema di gestione sottoscrittori del Financial Advisor Plugin.

## 1. Modifiche al Document Viewer Widget

### Problema Risolto
Il costo totale veniva azzerato al logout degli utenti non WordPress, perdendo i dati statistici importanti.

### Soluzione Implementata
- **File modificati:**
  - `includes/class-document-stats.php`
  - `includes/widgets/document-viewer-widget.php`
  - `assets/js/document-stats.js`

### Dettagli delle Modifiche

#### A. Database (class-document-stats.php)
- R<PERSON>sso il reset del campo `tot_cost` nelle funzioni di logout
- Il costo totale viene ora mantenuto nel database come dato statistico
- Modificate le funzioni `clean_user_data_before_logout()` per preservare `tot_cost`

#### B. Interfaccia Utente (document-viewer-widget.php)
- Nascosto il costo totale dall'UI con `style="display: none;"`
- Il campo rimane nel DOM per compatibilità ma non è visibile all'utente
- Aggiunto commento esplicativo: "Costo totale nascosto - mantenuto solo nel database per statistiche"

#### C. JavaScript (document-stats.js)
- Commentato il reset del `tot_cost` nella funzione `resetStatsOnWidgetLoad()`
- Modificato il localStorage per mantenere il `tot_cost` esistente durante il logout
- Preservazione dei dati statistici nelle funzioni di logout

## 2. Nuovo Widget Subscriber Management

### Funzionalità Implementate

#### A. Struttura del Widget
- **File principale:** `includes/widgets/subscriber-management-widget.php`
- **CSS:** `assets/css/subscriber-management-widget.css`
- **JavaScript:** `assets/js/subscriber-management-widget.js`

#### B. Menu Sinistro con 3 Sezioni

##### 1. Dati di Accesso
- Visualizzazione e modifica dati personali
- Campi: Nome, Cognome, Telefono (modificabili)
- Campi: Username, Email, Tipo Sottoscrizione (readonly)
- Integrazione con tabella `wpcd_user_subscription`

##### 2. Consumi e Statistiche
- **Statistiche visualizzate:**
  - Numero analisi effettuate
  - Token utilizzati
  - Costo totale (dato statistico)
  - Credito disponibile
- **Dettagli aggiuntivi:**
  - Data ultima analisi
  - Data registrazione
  - Costo attuale

##### 3. Ricarica Crediti
- **Importi predefiniti:** €10, €25, €50, €100
- **Importo personalizzato:** da €5 a €500
- **Metodi di pagamento:**
  - PayPal
  - Carta di Credito (Stripe)
  - Bonifico Bancario
- **Sistema di transazioni:** Log completo in database

### Database

#### Tabella Transazioni Crediti
```sql
CREATE TABLE wp_credit_transactions (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    subscriber_id bigint(20) unsigned NOT NULL,
    amount decimal(10,2) NOT NULL,
    transaction_type varchar(50) NOT NULL DEFAULT 'recharge',
    payment_method varchar(50) NOT NULL,
    status varchar(20) NOT NULL DEFAULT 'completed',
    transaction_id varchar(100) DEFAULT NULL,
    notes text DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY subscriber_id (subscriber_id),
    KEY transaction_type (transaction_type),
    KEY status (status)
);
```

### Utilizzo

#### Come Widget WordPress
1. Andare in **Aspetto > Widget**
2. Aggiungere "Subscriber Management Widget"
3. Configurare il titolo se necessario

#### Come Shortcode
```php
[subscriber_management title="Gestione Account"]
```

### Stile e Design

#### Caratteristiche del Design
- **Responsive:** Adattivo per mobile e desktop
- **Coerente:** Allineato con lo stile degli altri widget
- **Accessibile:** Icone FontAwesome e colori contrastanti
- **Interattivo:** Feedback visivo per tutte le azioni

#### Layout
- **Desktop:** Menu sinistro fisso + contenuto principale
- **Mobile:** Menu orizzontale scrollabile + contenuto sotto

### Sicurezza

#### Misure Implementate
- **Nonce verification** per tutte le richieste AJAX
- **Sanitizzazione** di tutti gli input utente
- **Controllo permessi** per accesso ai dati
- **Validazione** importi e metodi di pagamento

### Integrazione

#### Compatibilità
- **Utenti WordPress:** Integrazione con sistema esistente
- **Sottoscrittori esterni:** Supporto completo via cookie/sessioni
- **Database esistente:** Utilizza tabella `wpcd_user_subscription`

#### API AJAX
- `update_subscriber_data`: Aggiornamento dati personali
- `recharge_credits`: Gestione ricariche crediti

### File di Test

#### test-subscriber-widget.html
File HTML standalone per testare l'interfaccia del widget senza WordPress:
- Simula tutti i dati del widget
- Testa responsive design
- Verifica interazioni JavaScript

### Prossimi Sviluppi

#### Integrazioni Gateway Pagamento
- **PayPal:** Integrazione API PayPal
- **Stripe:** Integrazione API Stripe
- **Bonifico:** Sistema notifiche automatiche

#### Funzionalità Aggiuntive
- **Storico transazioni:** Visualizzazione dettagliata
- **Notifiche email:** Conferme ricariche
- **Limiti spesa:** Controlli automatici
- **Reportistica:** Export dati consumi

### Supporto e Manutenzione

#### Log e Debug
- Tutti gli errori vengono loggati nel sistema di debug esistente
- Messaggi di feedback chiari per l'utente
- Gestione errori graceful

#### Aggiornamenti
- Struttura modulare per facili aggiornamenti
- Backward compatibility mantenuta
- Database migrations automatiche

---

## Riepilogo Tecnico

### File Creati/Modificati

#### Nuovi File
- `includes/widgets/subscriber-management-widget.php`
- `assets/css/subscriber-management-widget.css`
- `assets/js/subscriber-management-widget.js`
- `test-subscriber-widget.html`

#### File Modificati
- `includes/class-document-stats.php`
- `includes/widgets/document-viewer-widget.php`
- `assets/js/document-stats.js`
- `includes/database-setup.php`
- `document-advisor-plugin.php`

### Funzionalità Chiave
1. ✅ Mantenimento costo totale nel database
2. ✅ Nascondere costo totale dall'UI
3. ✅ Widget gestione sottoscrittori completo
4. ✅ Sistema ricarica crediti
5. ✅ Integrazione database esistente
6. ✅ Design responsive e accessibile
7. ✅ Sicurezza e validazione complete

### Compatibilità
- ✅ WordPress 5.0+
- ✅ PHP 7.4+
- ✅ MySQL 5.7+
- ✅ Tutti i browser moderni
