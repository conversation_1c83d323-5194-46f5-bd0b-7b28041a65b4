<?php
if (!defined('ABSPATH')) {
    exit;
}

// Includi la classe Document_Stats
require_once plugin_dir_path(dirname(__FILE__)) . 'class-document-stats.php';

/**
 * Document Viewer Widget
 *
 * Widget for displaying the document viewer functionality in the sidebar.
 */
class Document_Viewer_Widget extends WP_Widget {
    public function __construct() {
        parent::__construct(
            'document_viewer_widget',
            __('Document Viewer Widget', 'document-viewer-plugin'),
            array('description' => __('A widget to upload and view documents with zoom functionality.', 'document-viewer-plugin'))
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        // Check if we have custom shortcode attributes
        $shortcode_atts = '';
        if (!empty($instance['custom_title'])) {
            $shortcode_atts = ' title="' . esc_attr($instance['custom_title']) . '"';
        }

        echo do_shortcode('[document_viewer' . $shortcode_atts . ']');
        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Document Viewer', 'document-viewer-plugin');
        $custom_title = !empty($instance['custom_title']) ? $instance['custom_title'] : __('Analisi Documento', 'document-viewer-plugin');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Widget Title:', 'document-viewer-plugin'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>"
                   type="text" value="<?php echo esc_attr($title); ?>">
            <small><?php _e('This is the title that appears above the widget in the sidebar.', 'document-viewer-plugin'); ?></small>
        </p>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('custom_title')); ?>"><?php _e('Document Viewer Title:', 'document-viewer-plugin'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('custom_title')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('custom_title')); ?>"
                   type="text" value="<?php echo esc_attr($custom_title); ?>">
            <small><?php _e('This title appears inside the document viewer component.', 'document-viewer-plugin'); ?></small>
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['custom_title'] = (!empty($new_instance['custom_title'])) ? sanitize_text_field($new_instance['custom_title']) : '';
        return $instance;
    }

    /**
     * Output della griglia principale delle statistiche utente
     *
     * @param bool $include_header Se includere anche l'intestazione
     * @return string HTML della griglia statistiche
     */
    private function get_stats_grid_html($include_header = true) {
        $user_id = get_current_user_id();
        $is_wp_user = $user_id > 0;
        $external_user_id = null;

        // Verifica se è un utente esterno non WordPress
        if (!$is_wp_user && isset($_COOKIE['fa_subscriber_login'])) {
            try {
                // Decodifica correttamente il cookie come nella classe FA_Access_Control
                $cookie_data = json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
                if (function_exists('dv_debug_log')) {
                    dv_debug_log('Cookie fa_subscriber_login decodificato: ' . print_r($cookie_data, true));
                }

                if (isset($cookie_data['id'])) {
                    $external_user_id = intval($cookie_data['id']);
                    // Aggiungi log per debug
                    if (function_exists('dv_debug_log')) {
                        dv_debug_log('Cookie decodificato correttamente, ID utente esterno: ' . $external_user_id);
                    }
                } else {
                    // Aggiungi log per debug
                    if (function_exists('dv_debug_log')) {
                        dv_debug_log('Cookie decodificato ma ID non trovato: ' . print_r($cookie_data, true));
                    }
                }
            } catch (Exception $e) {
                // Log dell'errore di decodifica
                if (function_exists('dv_debug_log')) {
                    dv_debug_log('Errore nella decodifica del cookie fa_subscriber_login: ' . $e->getMessage());
                }
            }
        }

        // Se non è né un utente WordPress né un utente esterno, mostra messaggio di login
        if (!$is_wp_user && !$external_user_id) {
            return '<div class="stats-not-logged">
                <p>Accedi per visualizzare le tue statistiche di utilizzo.</p>
            </div>';
        }

        // Ottieni le statistiche in base al tipo di utente
        $stats = null;
        if ($is_wp_user) {
            // Utente WordPress - usa il metodo standard
            $document_stats = new Document_Stats();
            $stats = $document_stats->get_user_stats($user_id);

            if (empty($stats)) {
                $document_stats->initialize_user_stats($user_id);
                $stats = $document_stats->get_user_stats($user_id);
            }
        } else {
            // Utente non WordPress - usa il metodo per utenti esterni
            $document_stats = new Document_Stats();
            $stats = $document_stats->get_external_user_stats($external_user_id);

            if (empty($stats)) {
                // Crea un oggetto stats vuoto per evitare errori
                $stats = (object)[
                    'analysis_count' => 0,
                    'tokens_used' => 0,
                    'credits_available' => 0,
                    'actual_cost' => 0,
                    'tot_cost' => 0
                ];
            }
        }

        // Formattazione numeri
        $analysis_count = $stats->analysis_count;
        $tokens_used = number_format($stats->tokens_used, 0, ',', '.');
        $credits_available = number_format((float)$stats->credits_available, 2, ',', '.');
        $actual_cost = number_format((float)$stats->actual_cost, 2, ',', '.');
        $tot_cost = number_format((float)$stats->tot_cost, 2, ',', '.');

        // Intestazione opzionale
        $header_html = '';
        if ($include_header) {
            if ($is_wp_user) {
                $user_data = get_userdata($user_id);
                $display_name = $user_data->display_name;
                $avatar = get_avatar($user_id, 40);
            } else {
                // Per utenti esterni, recupera i dati dalla tabella user_subscription
                global $wpdb;
                $users_table = $wpdb->prefix . 'user_subscription';
                $user_info = null;

                if ($external_user_id) {
                    // Usa la tabella con prefisso corretto
                    $users_table = 'wpcd_user_subscription';
                    $user_info = $wpdb->get_row($wpdb->prepare(
                        "SELECT name, surname, email, tipo_subscription FROM {$users_table} WHERE id = %d",
                        $external_user_id
                    ));
                }

                if ($user_info) {
                    // Formatta il nome completo se disponibile
                    $display_name = trim($user_info->name . ' ' . $user_info->surname);
                    if (empty($display_name)) {
                        $display_name = $user_info->email;
                    }

                    // Aggiungi il tipo di sottoscrizione
                    $subscription_type = !empty($user_info->tipo_subscription) ? $user_info->tipo_subscription : 'Standard';
                } else {
                    // Fallback ai dati del cookie se disponibili
                    $display_name = isset($cookie_data['email']) ? $cookie_data['email'] : 'Utente Esterno';
                    $subscription_type = isset($cookie_data['type']) ? $cookie_data['type'] : 'Standard';
                }

                $avatar = '<div class="avatar-placeholder">' . substr($display_name, 0, 1) . '</div>';
            }

            // Aggiungi il tipo di sottoscrizione per utenti esterni
            $subscription_html = '';
            if (!$is_wp_user && isset($subscription_type)) {
                $subscription_html = '<div id="user-subscription-type">Tipo: ' . esc_html($subscription_type) . '</div>';
            }

            $header_html = '<div class="user-details">
                <div id="user-avatar">' . $avatar . '</div>
                <div id="user-info">
                    <div id="user-name">' . esc_html($display_name) . '</div>
                    ' . $subscription_html . '
                </div>
            </div>';
        }

        // Griglia statistiche
        $html = $header_html . '
        <div class="stats-grid">
            <div class="stats-row usage-row">
                <div class="stats-item">
                    <div class="stats-label">
                        Analisi
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Numero totale di analisi effettuate</div>
                    </div>
                    <div class="stats-value" id="analyses-count">' . esc_html($analysis_count) . '</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">
                        Token
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Numero totale di token utilizzati in tutte le analisi</div>
                    </div>
                    <div class="stats-value" id="tokens-count">' . esc_html($tokens_used) . '</div>
                </div>
            </div>

            <div class="stats-row costs-row">
                <div class="stats-item cost-item">
                    <div class="stats-label">
                        Spesa Stimata
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Stima del costo basata sul numero di token utilizzati</div>
                    </div>
                    <div class="stats-value cost-highlight" id="cost-estimate">€' . esc_html($actual_cost) . '</div>
                </div>
                <div class="stats-item cost-item">
                    <div class="stats-label">
                        Spesa Effettiva
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Costo effettivo dell\'analisi completata</div>
                    </div>
                    <div class="stats-value cost-highlight" id="actual-cost">€' . esc_html($actual_cost) . '</div>
                </div>
            </div>

            <!-- Costo totale nascosto - mantenuto solo nel database per statistiche -->
            <div class="stats-row total-cost-row" style="display: none;">
                <div class="stats-item total-cost-item">
                    <div class="stats-label">
                        Spesa Totale
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Costo totale di tutte le analisi (dato statistico)</div>
                    </div>
                    <div class="stats-value total-cost-highlight" id="tot-cost">€' . esc_html($tot_cost) . '</div>
                </div>
            </div>

            <div class="stats-row credit-row">
                <div class="stats-item credit-item">
                    <div class="stats-label">
                        Credito Disponibile
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Credito disponibile per l\'esecuzione di nuove analisi</div>
                    </div>
                    <div class="stats-value credit-highlight" id="credits-available">€' . esc_html($credits_available) . '</div>
                </div>
            </div>
        </div>';

        return $html;
    }
}